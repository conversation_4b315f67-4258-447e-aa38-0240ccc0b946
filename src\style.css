* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    min-height: 100vh;
    background: transparent;
    overflow-x: hidden;
    position: relative;
    scroll-behavior: smooth;
}

#starCanvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
    background: #0a0a0a;
}

@keyframes fadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

@keyframes fadeOut {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

/* 流动光效动画 */
@keyframes flowingLight {
    0% {
        background-position: -200% 0;
    }
    50% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* 星星闪烁动画 */
@keyframes starTwinkle {
    0% {
        opacity: 0.3;
        transform: scale(0.8);
        text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
    }
    100% {
        opacity: 1;
        transform: scale(1.2);
        text-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
    }
}

.content {
    position: relative;
    z-index: 1; /* 降低z-index，确保不会遮挡音乐控制器 */
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    color: white;
    text-align: center;
    font-family: 'Arial', sans-serif;
    user-select: none;
}

.second-page {
    background: transparent;
}

.third-page {
    background: transparent;
}

.section-title {
    font-size: 2.5rem;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.4);
    margin-bottom: 1.5rem;
    color: rgba(255, 255, 255, 0.9);
}

.section-text {
    font-size: 1.1rem;
    opacity: 0.8;
    margin-bottom: 1rem;
    max-width: 600px;
    line-height: 1.6;
}

.title {
    font-size: 6rem;
    margin-bottom: 1rem;
    position: relative;
    /* 基础文字样式和星空光晕 */
    color: rgba(255, 255, 255, 0.9);
    text-shadow:
        0 0 5px rgba(255, 255, 255, 0.8),
        0 0 10px rgba(135, 206, 250, 0.6),
        0 0 20px rgba(173, 216, 230, 0.4),
        0 0 30px rgba(255, 255, 255, 0.2),
        0 0 40px rgba(135, 206, 250, 0.1);

    /* 流动光效渐变 */
    background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(135, 206, 250, 1) 25%,
        rgba(255, 255, 255, 1) 50%,
        rgba(173, 216, 230, 1) 75%,
        rgba(255, 255, 255, 0.9) 100%
    );
    background-size: 200% 100%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;

    /* 流动动画 */
    animation: flowingLight 3s ease-in-out infinite;
}

/* Title星星装饰效果 */
.title::before,
.title::after {
    content: '✦';
    position: absolute;
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.5rem;
    animation: starTwinkle 2s ease-in-out infinite alternate;
}

.title::before {
    left: -60px;
    top: 20%;
    animation-delay: 0s;
}

.title::after {
    right: -60px;
    top: 20%;
    animation-delay: 1s;
}

.subtitle {
    font-size: 1.2rem;
    opacity: 0.8;
}

/* 滚动指示器样式 */
.scroll-indicator {
    position: absolute;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.scroll-indicator:hover {
    transform: translateX(-50%) translateY(-5px);
}

.scroll-arrow span {
    display: block;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin-bottom: 10px;
    font-weight: 300;
    letter-spacing: 1px;
}

.arrow-down {
    position: relative;
    width: 2px;
    height: 30px;
    margin: 0 auto;
}



.arrow-head {
    position: absolute;
    bottom: 0;
    left: -3px;
    width: 8px;
    height: 8px;
    border-right: 2px solid rgba(255, 255, 255, 0.8);
    border-bottom: 2px solid rgba(255, 255, 255, 0.8);
    transform: rotate(45deg);
    animation: arrowBounce 2s infinite ease-in-out;
}

/* 向上箭头样式 */
.arrow-up {
    position: relative;
    width: 2px;
    height: 30px;
    margin: 0 auto;
}

.arrow-up .arrow-line {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 2px;
    height: 20px;
    background: linear-gradient(to top, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.8));
    animation: arrowPulseUp 2s infinite ease-in-out;
}

.arrow-head-up {
    position: absolute;
    top: 0;
    left: -3px;
    width: 8px;
    height: 8px;
    border-left: 2px solid rgba(255, 255, 255, 0.8);
    border-top: 2px solid rgba(255, 255, 255, 0.8);
    transform: rotate(45deg);
    animation: arrowBounceUp 2s infinite ease-in-out;
}

@keyframes arrowPulse {
    0%, 100% {
        opacity: 0.3;
        transform: scaleY(0.8) translateX(0);
    }
    50% {
        opacity: 1;
        transform: scaleY(1) translateX(2px);
    }
}

@keyframes arrowPulseUp {
    0%, 100% {
        opacity: 0.3;
        transform: scaleY(0.8) translateX(0);
    }
    50% {
        opacity: 1;
        transform: scaleY(1) translateX(-2px);
    }
}

@keyframes arrowBounce {
    0%, 100% {
        transform: rotate(45deg) translateX(0);
        opacity: 0.6;
    }
    50% {
        transform: rotate(45deg) translateX(3px);
        opacity: 1;
    }
}

@keyframes arrowBounceUp {
    0%, 100% {
        transform: rotate(45deg) translateX(0);
        opacity: 0.6;
    }
    50% {
        transform: rotate(45deg) translateX(-3px);
        opacity: 1;
    }
}

/* 侧边菜单样式 */
.sidebar {
    position: fixed;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10000;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    /* 确保容器不会影响子元素的定位 */
    width: 0;
    height: 0;
}

/* 菜单触发器（收起时的白色竖线） */
.sidebar-trigger {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%) translateX(0px);
    /* 扩大触发区域 */
    width: 40px;
    height: 100px;
    /* 移除背景色，改用伪元素显示白线 */
    background: transparent;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
    /* 添加padding增加触发范围 */
    padding: 10px;

    /* 调试边框 - 取消注释来查看边界 */
    /* border: 1px solid red !important; */
    /* background: rgba(255, 0, 0, 0.5) !important; */
}

/* 使用伪元素显示实际的白线 */
.sidebar-trigger::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 4px;
    height: 60px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 2px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

/* 悬停时只改变白线本身的样式 */
.sidebar-trigger:hover::before {
    background: rgba(255, 255, 255, 1);
    width: 5px;
    height: 120px;
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
    transform: translate(-50%, -50%) scaleY(1.1);
}

/* 菜单展开时白线的伸长效果 */
.sidebar.expanding .sidebar-trigger::before {
    height: 100px;
    width: 5px;
    background: rgba(255, 255, 255, 1);
    box-shadow:
        0 0 20px rgba(255, 255, 255, 0.8),
        0 0 40px rgba(255, 255, 255, 0.4);
    transform: translate(-50%, -50%) scaleY(1.8);
    animation: expandPulse 0.2s ease-out;
}

/* 白线展开脉冲动画 */
@keyframes expandPulse {
    0% {
        height: 120px;
        box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
    }
    50% {
        height: 120px;
        box-shadow:
            0 0 25px rgba(255, 255, 255, 0.9),
            0 0 50px rgba(255, 255, 255, 0.5);
    }
    100% {
        height: 100px;
        box-shadow:
            0 0 20px rgba(255, 255, 255, 0.8),
            0 0 40px rgba(255, 255, 255, 0.4);
    }
}

/* 菜单内容容器 */
.sidebar-content {
    margin-left: -40px;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%) translateX(-100%);
    width: 280px;
    /* 设置与网页等高 */
    height: 100vh;
    background: rgba(255, 255, 255, 0);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 0; /* 移除padding，让内部元素控制 */
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    /* 移除flexbox，让滚动容器占满整个空间 */
    overflow: hidden;
}

/* 菜单展开状态 */
.sidebar.expanded .sidebar-content {
    transform: translateY(-50%) translateX(40px);
    opacity: 1;
    visibility: visible;
}

.sidebar.expanded .sidebar-trigger::before {
    opacity: 0;
    visibility: hidden;
    transform: translate(-50%, -50%) scaleY(0);
}

/* 菜单展开过程中的状态 */
.sidebar.expanding .sidebar-content {
    transform: translateY(-50%) translateX(-80px);
    opacity: 0;
    visibility: hidden;
}



/* 滚动容器 */
.sidebar-scroll-container {
    /* 占满整个菜单内容区域 */
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 0 5px 0 0; /* 为滚动条留出空间 */
    /* 添加平滑滚动 */
    scroll-behavior: smooth;
    /* 优化触摸滚动（移动设备） */
    -webkit-overflow-scrolling: touch;
    /* 添加渐变遮罩效果 */
    mask: linear-gradient(
        to bottom,
        transparent 0%,
        black 5%,
        black 95%,
        transparent 100%
    );
    -webkit-mask: linear-gradient(
        to bottom,
        transparent 0%,
        black 5%,
        black 95%,
        transparent 100%
    );
}

/* 导航菜单 */
.sidebar-nav {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 15px 20px; /* 减少顶部padding */
    /* 添加更多内容用于测试滚动 */
    min-height: 100%;
}

/* 自定义滚动条样式 */
.sidebar-scroll-container::-webkit-scrollbar {
    width: 6px;
}

.sidebar-scroll-container::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.sidebar-scroll-container::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    transition: background 0.3s ease;
}

.sidebar-scroll-container::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Firefox滚动条样式 */
.sidebar-scroll-container {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);
}

/* 滚动指示器 */
.sidebar-content::after {
    content: '';
    position: absolute;
    bottom: 10px;
    right: 15px;
    width: 20px;
    height: 3px;
    background: rgba(255, 255, 255, 0.4);
    border-radius: 2px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

/* 当内容可滚动时显示指示器 */
.sidebar-content.scrollable::after {
    opacity: 1;
    animation: scrollHint 2s ease-in-out infinite;
}

@keyframes scrollHint {
    0%, 100% {
        transform: translateY(0);
        opacity: 0.4;
    }
    50% {
        transform: translateY(-3px);
        opacity: 0.8;
    }
}

.sidebar-link {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    border-radius: 10px;
    transition: all 0.3s ease;
    font-size: 0.95rem;
    position: relative;
    overflow: hidden;
}

.sidebar-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 1);
    transform: translateX(5px);
}

.sidebar-link:active {
    transform: translateX(5px) scale(0.98);
}

/* 菜单项光效 */
.sidebar-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0.1),
        rgba(255, 255, 255, 0.05)
    );
    transition: width 0.3s ease;
}

.sidebar-link:hover::before {
    width: 100%;
}

/* 菜单项动画延迟 */
.sidebar-link:nth-child(1) { transition-delay: 0.1s; }
.sidebar-link:nth-child(2) { transition-delay: 0.15s; }
.sidebar-link:nth-child(3) { transition-delay: 0.2s; }
.sidebar-link:nth-child(4) { transition-delay: 0.25s; }
.sidebar-link:nth-child(5) { transition-delay: 0.3s; }

/* 菜单展开时的动画 */
.sidebar.expanded .sidebar-link {
    animation: slideInFromLeft 0.4s ease forwards;
}

@keyframes slideInFromLeft {
    0% {
        opacity: 0;
        transform: translateX(-20px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 背景音乐控制按钮 */
.music-control {
    position: fixed !important; /* 改为固定定位，跟随窗口 */
    top: 30px !important;
    right: 30px !important;
    z-index: 999999 !important; /* 极高的z-index确保在最顶层 */
    cursor: pointer !important;
    transition: all 0.3s ease;
    /* 扩大悬停区域以包含音量控制条 */
    padding-bottom: 100px;
    /* 确保元素可以接收鼠标事件 */
    pointer-events: auto !important;
    /* 确保始终可见 */
    opacity: 1 !important;
    visibility: visible !important;
    /* 确保不会被transform影响 */
    transform: none !important;
    /* 调试背景已移除 */
}

.music-control:hover .vinyl-record {
    border-color: rgba(255, 255, 255, 1) !important;
    transform: scale(1.1) !important;
}

.music-control:hover .vinyl-record::before {
    border-color: rgba(255, 255, 255, 0.8) !important;
}

.music-control:hover .vinyl-record::after {
    border-color: rgba(255, 255, 255, 0.6) !important;
}

.music-control:hover .vinyl-center {
    border-color: rgba(255, 255, 255, 1) !important;
}

.vinyl-record {
    width: 40px;
    height: 40px;
    border: 2px solid rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    position: relative;
    transition: all 0.3s ease;
}

.vinyl-record.playing {
    animation: vinylSpin 3s linear infinite;
}

.vinyl-record::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 1px solid rgba(255, 255, 255, 0.6);
    border-radius: 50%;
}

.vinyl-record::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    border: 1px solid rgba(255, 255, 255, 0.4);
    border-radius: 50%;
}

.vinyl-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 6px;
    height: 6px;
    border: 1px solid rgba(255, 255, 255, 0.8);
    border-radius: 50%;
}

.vinyl-hole {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 2px;
    height: 2px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
}

/* 音符特效容器 */
.music-notes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100px;
    height: 100px;
    pointer-events: none;
    overflow: visible;
}

/* 音量控制条 */
.volume-control {
    position: absolute;
    top: 60px;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    pointer-events: none;
    padding: 10px; /* 增加悬停区域 */
}

.music-control:hover .volume-control {
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: auto !important;
}

.volume-slider {
    width: 8px;
    height: 80px;
    position: relative;
    cursor: pointer;
}

.volume-track {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.6);
    border-radius: 4px;
}

.volume-fill {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 30%;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 0 0 3px 3px;
    transition: height 0.2s ease;
}

.volume-handle {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 12px;
    height: 4px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 2px;
    transition: all 0.2s ease;
    bottom: calc(30% - 2px);
}

.volume-handle:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateX(-50%) scale(1.2);
}

/* 音符样式 */
.music-note {
    position: absolute;
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    font-weight: bold;
    pointer-events: none;
    animation: noteFloat 3s ease-out forwards;
}

/* 音符浮动动画 */
@keyframes noteFloat {
    0% {
        opacity: 1;
        transform: translate(0, 0) scale(0.5);
    }
    50% {
        opacity: 0.8;
        transform: translate(var(--random-x), var(--random-y)) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(calc(var(--random-x) * 2), calc(var(--random-y) * 2)) scale(0.3);
    }
}

/* 唱片旋转动画 */
@keyframes vinylSpin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 星座连线效果 */
.constellation {
    position: absolute;
    stroke: rgba(255, 255, 255, 0.3);
    stroke-width: 1;
    fill: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .title {
        font-size: 2rem;
    }

    /* 移动设备上调整星星装饰位置 */
    .title::before {
        left: -30px;
        font-size: 1rem;
    }

    .title::after {
        right: -30px;
        font-size: 1rem;
    }

    .subtitle {
        font-size: 1rem;
    }

    /* 移动设备侧边菜单优化 */
    .sidebar-content {
        width: 250px;
        height: 350px;
        max-height: 80vh; /* 移动设备占用更多屏幕空间 */
    }

    .sidebar-nav {
        padding: 15px;
    }

    .sidebar.expanded .sidebar-content {
        transform: translateY(-50%) translateX(25px);
    }

    .sidebar-trigger {
        transform: translateY(-50%) translateX(15px);
        /* 移动设备触发区域稍小 */
        width: 18px;
        height: 70px;
        padding: 8px;
    }

    .sidebar-trigger::before {
        /* 移动设备白线稍小 */
        height: 35px;
    }

    /* 移动设备白线动画优化 */
    .sidebar.expanding .sidebar-trigger::before {
        height: 70px;
    }

    .sidebar.collapsing .sidebar-trigger::before {
        height: 35px;
        /* 删除了移动设备滑入动画 */
    }

    /* 移动设备专用的滑入动画 - 已删除 */

    .sidebar-link {
        padding: 10px 12px;
        font-size: 0.9rem;
    }
    
    .scroll-indicator {
        bottom: 30px;
    }
    
    .scroll-arrow span {
        font-size: 12px;
    }
} 
