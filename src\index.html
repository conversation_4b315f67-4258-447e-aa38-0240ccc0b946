<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Starry Sky</title>
    <link rel="stylesheet" href="style.css">
</head>

<body>
    <canvas id="starCanvas"></canvas>

    <!-- 侧边菜单 -->
    <div class="sidebar" id="sidebar">
        <!-- 菜单触发器（收起时的白色竖线） -->
        <div class="sidebar-trigger" id="sidebarTrigger"></div>

        <!-- 菜单内容 -->
        <div class="sidebar-content" id="sidebarContent">
            <div class="sidebar-header">
                <h3>导航菜单</h3>
                <button class="sidebar-close" id="sidebarClose">-</button>
            </div>
            <nav class="sidebar-nav">
                <a href="#" class="sidebar-link" data-target="first">🏠 首页</a>
                <a href="#" class="sidebar-link" data-target="second">🔍 探索</a>
                <a href="#" class="sidebar-link" data-target="third">🌟 关于</a>
                <a href="#" class="sidebar-link">⚙️ 设置</a>
                <a href="#" class="sidebar-link">📞 联系</a>
            </nav>
        </div>
    </div>

    <!-- 背景音乐控制按钮 - 移到body根级别 -->
    <div class="music-control" id="musicControl">
        <div class="vinyl-record">
            <div class="vinyl-center"></div>
            <div class="vinyl-hole"></div>
        </div>
        <!-- 音符特效容器 -->
        <div class="music-notes" id="musicNotes"></div>
        <!-- 音量控制条 -->
        <div class="volume-control" id="volumeControl">
            <div class="volume-slider">
                <div class="volume-track"></div>
                <div class="volume-fill" id="volumeFill"></div>
                <div class="volume-handle" id="volumeHandle"></div>
            </div>
        </div>
    </div>

    <div class="content">
        <div>
            <h1 class="title">CURSOR</h1>
            <p class="subtitle">Built to make you extraordinarily productive, Cursor is the best way to code with AI.</p>
            <div class="scroll-indicator" id="scrollIndicator">
                <div class="scroll-arrow">
                    <span>探索更多</span>
                    <div class="arrow-down">
                        <div class="arrow-head"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="content second-page">
        <div class="main-card">
            <h2 class="section-title">探索更多功能</h2>
            <p class="section-text">这里是第二个页面内容，展示滚动功能的效果。</p>
            <p class="section-text">您可以使用鼠标滚轮或下方的按钮来浏览不同的页面内容。</p>
        </div>
    </div>

    <div class="content third-page">
        <div>
            <h2 class="section-title">无限可能</h2>
            <p class="section-text">在这个星空下，每一行代码都承载着创造的力量。</p>
            <p class="section-text">让我们一起探索编程的无限可能，创造属于未来的作品。</p>
            <div class="scroll-indicator" id="scrollIndicatorThird">
                <div class="scroll-arrow">
                    <span>返回顶部</span>
                    <div class="arrow-up">
                        <div class="arrow-head-up"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 调试面板 -->
    <div id="debugPanel" style="
        position: fixed;
        top: 10px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 15px;
        border-radius: 8px;
        font-family: monospace;
        font-size: 11px;
        z-index: 99999;
        display: none;
        max-width: 500px;
        border: 1px solid #333;
    ">
        <div style="margin-bottom: 15px; font-weight: bold; color: #4CAF50;">🎯 缩回动画路径调试</div>

        <div style="margin-bottom: 10px; color: #FFC107;">动画关键帧位置调试：</div>

        <div style="margin-bottom: 8px;">
            <label>0% 起始位置 X: <input type="range" id="anim0X" min="-200" max="0" value="-120" style="width: 120px;"> <span id="anim0XValue">-120px</span></label>
        </div>

        <div style="margin-bottom: 8px;">
            <label>30% 位置 X: <input type="range" id="anim30X" min="-80" max="0" value="-40" style="width: 120px;"> <span id="anim30XValue">-40px</span></label>
        </div>

        <div style="margin-bottom: 8px;">
            <label>70% 位置 X: <input type="range" id="anim70X" min="-10" max="20" value="5" style="width: 120px;"> <span id="anim70XValue">5px</span></label>
        </div>

        <div style="margin-bottom: 15px;">
            <label>100% 终点 X: <input type="range" id="anim100X" min="15" max="25" value="20" style="width: 120px;"> <span id="anim100XValue">20px</span></label>
        </div>

        <div style="margin-bottom: 10px;">
            <button onclick="updateAnimationCSS()" style="background: #4CAF50; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">应用动画路径</button>
            <button onclick="testSlideAnimation()" style="background: #2196F3; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">测试滑入动画</button>
            <button onclick="showAnimationPath()" style="background: #FF9800; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">显示路径轨迹</button>
        </div>

        <div style="margin-bottom: 10px;">
            <button onclick="toggleDebugMode()" style="background: #9C27B0; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">切换调试模式</button>
            <button onclick="resetAnimationPath()" style="background: #607D8B; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">重置路径</button>
        </div>

        <div id="animationInfo" style="margin-top: 10px; font-size: 10px; color: #ccc; border-top: 1px solid #333; padding-top: 10px;"></div>
    </div>

    <script src="script.js"></script>

    <script>
        // 调试工具
        let debugMode = false;
        let pathMarkers = [];

        function toggleDebugMode() {
            debugMode = !debugMode;
            const trigger = document.getElementById('sidebarTrigger');
            if (debugMode) {
                trigger.style.border = '2px solid red';
                trigger.style.background = 'rgba(255, 0, 0, 0.3)';
                document.getElementById('debugPanel').style.display = 'block';
                initializeSliders();
            } else {
                trigger.style.border = '';
                trigger.style.background = '';
                document.getElementById('debugPanel').style.display = 'none';
                clearPathMarkers();
            }
        }

        function initializeSliders() {
            // 初始化所有滑块的事件监听器
            const sliders = ['anim0X', 'anim30X', 'anim70X', 'anim100X'];
            sliders.forEach(id => {
                const slider = document.getElementById(id);
                const valueSpan = document.getElementById(id + 'Value');
                slider.addEventListener('input', function(e) {
                    valueSpan.textContent = e.target.value + 'px';
                    updateAnimationInfo();
                });
            });
        }

        function updateAnimationInfo() {
            const values = {
                start: document.getElementById('anim0X').value,
                p30: document.getElementById('anim30X').value,
                p70: document.getElementById('anim70X').value,
                end: document.getElementById('anim100X').value
            };

            document.getElementById('animationInfo').innerHTML = `
                动画路径: ${values.start}px → ${values.p30}px → ${values.p70}px → ${values.end}px<br>
                总移动距离: ${Math.abs(values.end - values.start)}px<br>
                动画类型: 线性滑入（无回弹）
            `;
        }

        function updateAnimationCSS() {
            const values = {
                p0: document.getElementById('anim0X').value,
                p30: document.getElementById('anim30X').value,
                p70: document.getElementById('anim70X').value,
                p100: document.getElementById('anim100X').value
            };

            // 动态创建新的CSS动画
            const styleId = 'debugAnimationStyle';
            let style = document.getElementById(styleId);
            if (!style) {
                style = document.createElement('style');
                style.id = styleId;
                document.head.appendChild(style);
            }

            style.textContent = `
                @keyframes slideInFromLeftDebug {
                    0% {
                        transform: translateY(-50%) translateX(${values.p0}px);
                        opacity: 0;
                        width: 1px;
                        height: 10px;
                        background: rgba(255, 255, 255, 0.2);
                        box-shadow: 0 0 3px rgba(255, 255, 255, 0.1);
                    }
                    30% {
                        transform: translateY(-50%) translateX(${values.p30}px);
                        opacity: 0.4;
                        width: 2px;
                        height: 25px;
                        background: rgba(255, 255, 255, 0.5);
                        box-shadow: 0 0 6px rgba(255, 255, 255, 0.3);
                    }
                    70% {
                        transform: translateY(-50%) translateX(${values.p70}px);
                        opacity: 0.8;
                        width: 3px;
                        height: 35px;
                        background: rgba(255, 255, 255, 0.8);
                        box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
                    }
                    100% {
                        transform: translateY(-50%) translateX(${values.p100}px);
                        opacity: 1;
                        width: 4px;
                        height: 40px;
                        background: rgba(255, 255, 255, 0.8);
                        box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
                    }
                }

                .sidebar.collapsing .sidebar-trigger {
                    animation: slideInFromLeftDebug 0.5s ease-out forwards !important;
                }
            `;

            console.log('动画CSS已更新:', values);
        }

        function testSlideAnimation() {
            if (window.sidebarController) {
                // 先确保菜单是展开的
                if (!window.sidebarController.isExpanded) {
                    window.sidebarController.openSidebar();
                    setTimeout(() => {
                        window.sidebarController.closeSidebar();
                    }, 1000);
                } else {
                    window.sidebarController.closeSidebar();
                }
            }
        }

        function showAnimationPath() {
            clearPathMarkers();
            const sidebar = document.getElementById('sidebar');
            const sidebarRect = sidebar.getBoundingClientRect();

            const keyframes = [
                { percent: '0%', x: document.getElementById('anim0X').value, color: '#f44336' },
                { percent: '30%', x: document.getElementById('anim30X').value, color: '#ff9800' },
                { percent: '70%', x: document.getElementById('anim70X').value, color: '#4caf50' },
                { percent: '100%', x: document.getElementById('anim100X').value, color: '#9c27b0' }
            ];

            keyframes.forEach((frame, index) => {
                const marker = document.createElement('div');
                marker.style.cssText = `
                    position: fixed;
                    left: ${sidebarRect.left + parseInt(frame.x)}px;
                    top: ${sidebarRect.top}px;
                    width: 8px;
                    height: 8px;
                    background: ${frame.color};
                    border: 2px solid white;
                    border-radius: 50%;
                    z-index: 99998;
                    pointer-events: none;
                `;

                const label = document.createElement('div');
                label.style.cssText = `
                    position: fixed;
                    left: ${sidebarRect.left + parseInt(frame.x) + 12}px;
                    top: ${sidebarRect.top - 5}px;
                    color: ${frame.color};
                    font-size: 10px;
                    font-weight: bold;
                    background: rgba(0,0,0,0.7);
                    padding: 2px 4px;
                    border-radius: 2px;
                    z-index: 99998;
                    pointer-events: none;
                `;
                label.textContent = frame.percent;

                document.body.appendChild(marker);
                document.body.appendChild(label);
                pathMarkers.push(marker, label);
            });

            setTimeout(clearPathMarkers, 5000); // 5秒后自动清除
        }

        function clearPathMarkers() {
            pathMarkers.forEach(marker => {
                if (marker.parentNode) {
                    marker.parentNode.removeChild(marker);
                }
            });
            pathMarkers = [];
        }

        function resetAnimationPath() {
            document.getElementById('anim0X').value = -120;
            document.getElementById('anim30X').value = -40;
            document.getElementById('anim70X').value = 5;
            document.getElementById('anim100X').value = 20;

            // 更新显示值
            document.getElementById('anim0XValue').textContent = '-120px';
            document.getElementById('anim30XValue').textContent = '-40px';
            document.getElementById('anim70XValue').textContent = '5px';
            document.getElementById('anim100XValue').textContent = '20px';

            updateAnimationInfo();
        }

        // 按F12开启调试模式
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F12') {
                e.preventDefault();
                toggleDebugMode();
            }
        });
    </script>
</body>

</html>